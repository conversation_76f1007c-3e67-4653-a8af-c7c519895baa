Prompt <PERSON><PERSON> Thuật cho Agent Phát Triển <PERSON>ần <PERSON> (React & Spring Boot)
🎯 Mục Tiêu
Agent được giao nhiệm vụ phân tích tài liệu thiết kế hệ thống (Use Case, Class, Sequence Diagrams) trong file PDF và tạo ra mã nguồn, tập trung vào việc triển khai mô-đun Quản lý Order và Quản lý Sản phẩm sử dụng stack công nghệ ReactJS (Frontend) và Spring Boot (Backend).

💻 Yêu Cầu Công Nghệ
Thành phần	Công nghệ	Chi tiết
Backend (API)	Spring Boot (Java)	Sử dụng kiến trúc Layered (Controller, Service, Repository) và JPA/Hibernate.
Frontend (UI)	ReactJS	Sử dụng Functional Components và Hooks hiện đại.
Database	SQL (Implicit)	Tương tác thông qua các Repository (DAL).

<PERSON><PERSON><PERSON> sang Trang tính
🛠️ Yêu Cầu <PERSON>ể<PERSON> (Spring Boot)
Agent phải tạo các tệp mã nguồn <PERSON> (Classes, Interfaces, Annotations) theo nguyên tắc SOLID và Clean Architecture.

1. Mô hình Dữ liệu (Entity & Enum)
Tạo các Entity và Enum dựa trên Class Diagram.

Tên Class/Enum	Yêu cầu Đặc biệt
User (Abstract) & Staff, Manager	
Sử dụng cơ chế kế thừa phù hợp (ví dụ: 

@Inheritance).


Order & OrderItem		
Order phải có trạng thái (status: OrderStatus). 


OrderItem phải lưu priceAtTimeOfOrder.


Product	
Bao gồm 

status: ProductStatus.


EOrderStatus	
Định nghĩa các trạng thái: 

PENDING, PAID, COMPLETED.


Xuất sang Trang tính
2. Logic Nghiệp vụ & Controllers
Triển khai Controller và Service theo Use Case và Sequence Diagram.

Use Case ID	Controller & Endpoint	Yêu cầu Logic Nghiêm ngặt
UC_STAFF_001 (Tạo Order)	POST /api/orders	
Transaction (Commit/Rollback) để đảm bảo Order và OrderItem được lưu đồng thời. Xác thực 

ProductStatus = AVAILABLE.





UC_STAFF_003 (Xác nhận TT)	PUT /api/orders/{id}/pay	
Chốt chặn nghiệp vụ: Chỉ cho phép chuyển trạng thái PENDING→PAID. Cấm can thiệp nếu trạng thái là PAID hoặc COMPLETED.



UC_STAFF_004 (Cập nhật TT)	PUT /api/orders/{id}/complete		
Chốt chặn nghiệp vụ: Chỉ cho phép chuyển trạng thái PAID→COMPLETED.





UC_MGR_002, UC_MGR_004 (Báo cáo)	GET /api/reports/revenue & GET /api/reports/bestsellers	
Logic tính toán chỉ dựa trên các Order có trạng thái 

PAID hoặc COMPLETED.


UC_MGR_003 (QL Nhân viên)	POST /api/users/staff	
Bắt buộc: Mật khẩu phải được Hash trước khi lưu. 

Không được phép Hard Delete tài khoản.



Xuất sang Trang tính
🎨 Yêu Cầu Triển Khai Frontend (ReactJS)
Tạo các component React cho các giao diện chính (UI Layer ), tập trung vào trải nghiệm người dùng tối ưu.

1. Giao diện Nhân viên (Staff UI)
Component	Use Case	Yêu cầu Chức năng
OrderCreationUI	UC_STAFF_001, UC_STAFF_005	
Form chọn sản phẩm & số lượng, tính toán tổng tiền động, hiển thị mã Order mới sau khi tạo thành công.





OrderListUI	UC_STAFF_005	
Bảng hiển thị danh sách Order trong ngày, có bộ lọc theo trạng thái PENDING/PAID.

OrderActionComponent	UC_STAFF_003, UC_STAFF_004	Nút "Thanh toán" (chỉ hiện khi status PENDING) và nút "Hoàn thành" (chỉ hiện khi status PAID).

Xuất sang Trang tính
2. Giao diện Quản lý (Manager UI)
Component	Use Case	Yêu cầu Chức năng
ProductManagementUI	UC_MGR_001	
Giao diện CRUD (Thêm, Sửa, Xóa mềm) Sản phẩm.





ReportUI	UC_MGR_002, UC_MGR_004	
Giao diện cho phép chọn 

khoảng thời gian để xem Báo cáo Doanh thu và Thống kê Sản phẩm bán chạy.



Xuất sang Trang tính
🚫 Nguyên tắc Phát triển Bắt buộc

Phản bác External Subsystem: Không triển khai bất kỳ đối tượng, Controller, hoặc logic kết nối nào với Grab hay Shopee. Toàn bộ đơn hàng online được xử lý như một đơn hàng do 

Nhân viên nhập vào hệ thống (theo kịch bản "Human API").


Xử lý Lỗi (Error Handling): Mỗi tầng (UI → Controller → Service → Repository) phải có cơ chế bắt và báo cáo lỗi rõ ràng (try-catch và Alternative Flows/Exceptions).






Tối ưu truy vấn: Đảm bảo các truy vấn báo cáo và tìm kiếm là hiệu quả (ví dụ: sử dụng Indexing cho các cột createdAt, status).



